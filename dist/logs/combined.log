{"context":"NestFactory","level":"info","message":"Starting Nest application...","timestamp":"2025-07-02T09:32:21.716Z"}
{"context":"TranslationService","level":"info","message":"加载翻译配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-02T09:32:21.737Z"}
{"context":"TranslationService","level":"info","message":"加载主题提取配置 - 模式: default, 模型: qwen-turbo","timestamp":"2025-07-02T09:32:21.737Z"}
{"context":"TranslationService","level":"info","message":"翻译服务已初始化 - 翻译模型: qwen-turbo, 主题提取模型: qwen-turbo","timestamp":"2025-07-02T09:32:21.739Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.741Z"}
{"context":"InstanceLoader","level":"info","message":"PassportModule dependencies initialized","timestamp":"2025-07-02T09:32:21.741Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.741Z"}
{"context":"LLMConfigService","level":"info","message":"LLM配置管理服务初始化完成","timestamp":"2025-07-02T09:32:21.743Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigHostModule dependencies initialized","timestamp":"2025-07-02T09:32:21.744Z"}
{"context":"InstanceLoader","level":"info","message":"DiscoveryModule dependencies initialized","timestamp":"2025-07-02T09:32:21.745Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-02T09:32:21.745Z"}
{"context":"InstanceLoader","level":"info","message":"ConfigModule dependencies initialized","timestamp":"2025-07-02T09:32:21.745Z"}
{"context":"InstanceLoader","level":"info","message":"LLMConfigModule dependencies initialized","timestamp":"2025-07-02T09:32:21.746Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-02T09:32:21.746Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-02T09:32:21.746Z"}
{"context":"InstanceLoader","level":"info","message":"ScheduleModule dependencies initialized","timestamp":"2025-07-02T09:32:21.746Z"}
{"context":"InstanceLoader","level":"info","message":"ElasticsearchModule dependencies initialized","timestamp":"2025-07-02T09:32:21.748Z"}
{"context":"InstanceLoader","level":"info","message":"JwtModule dependencies initialized","timestamp":"2025-07-02T09:32:21.748Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"TLE查询配置加载完成: 时间窗口=30分钟, 最大记录数=50000","timestamp":"2025-07-02T09:32:21.753Z"}
{"context":"InstanceLoader","level":"info","message":"OrbitAnalysisModule dependencies initialized","timestamp":"2025-07-02T09:32:21.754Z"}
{"context":"NewsSchedulerService","level":"info","message":"新闻定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 5,12,18 * * *\",\"timezone\":\"Asia/Shanghai\"}","timestamp":"2025-07-02T09:32:21.755Z"}
{"context":"InstanceLoader","level":"info","message":"ESModule dependencies initialized","timestamp":"2025-07-02T09:32:21.756Z"}
{"context":"InstanceLoader","level":"info","message":"NewsSchedulerModule dependencies initialized","timestamp":"2025-07-02T09:32:21.756Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteTilesModule dependencies initialized","timestamp":"2025-07-02T09:32:21.756Z"}
{"context":"ElasticsearchDebrisEventService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.788Z"}
{"context":"ElasticsearchOrbitService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchRocketService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchBaseService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchSatelliteService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchNewsService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchConstellationService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"ElasticsearchLaunchService","level":"info","message":"成功连接到Elasticsearch: node-1","timestamp":"2025-07-02T09:32:21.789Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmCoreModule dependencies initialized","timestamp":"2025-07-02T09:32:21.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.832Z"}
{"context":"InstanceLoader","level":"info","message":"TypeOrmModule dependencies initialized","timestamp":"2025-07-02T09:32:21.832Z"}
{"context":"InstanceLoader","level":"info","message":"UserModule dependencies initialized","timestamp":"2025-07-02T09:32:21.833Z"}
{"context":"InstanceLoader","level":"info","message":"AppModule dependencies initialized","timestamp":"2025-07-02T09:32:21.833Z"}
{"context":"SatelliteSchedulerService","level":"info","message":"卫星数据定时任务服务初始化完成，配置: {\"enabled\":true,\"cronExpression\":\"0 3 * * *\",\"timezone\":\"Asia/Shanghai\",\"saveToDatabase\":true}","timestamp":"2025-07-02T09:32:21.833Z"}
{"context":"InstanceLoader","level":"info","message":"DatabaseModule dependencies initialized","timestamp":"2025-07-02T09:32:21.834Z"}
{"context":"InstanceLoader","level":"info","message":"AggregationTaskModule dependencies initialized","timestamp":"2025-07-02T09:32:21.834Z"}
{"context":"InstanceLoader","level":"info","message":"AuthModule dependencies initialized","timestamp":"2025-07-02T09:32:21.834Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteModule dependencies initialized","timestamp":"2025-07-02T09:32:21.834Z"}
{"context":"InstanceLoader","level":"info","message":"SatelliteSchedulerModule dependencies initialized","timestamp":"2025-07-02T09:32:21.834Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteAggregationController {/local/satellite-aggregation}:","timestamp":"2025-07-02T09:32:21.905Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/status, GET} route","timestamp":"2025-07-02T09:32:21.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite-aggregation/running, GET} route","timestamp":"2025-07-02T09:32:21.906Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/auth}:","timestamp":"2025-07-02T09:32:21.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/register, POST} route","timestamp":"2025-07-02T09:32:21.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/login, POST} route","timestamp":"2025-07-02T09:32:21.906Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/role, PUT} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users/:id/permissions, GET} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/users, GET} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/profile, GET} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/pending-users, GET} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/approval, PUT} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/user-approval-history, GET} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/admin/users/:id/delete, DELETE} route","timestamp":"2025-07-02T09:32:21.907Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/auth/change-password, PUT} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisEventController {/api/debris-events}:","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/debris-events/search, POST} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchConstellationController {/constellation}:","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/search, POST} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/names, GET} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/organizations, GET} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/constellation/purposes, GET} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchCorrelationController {/correlation}:","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/debris-to-event, POST} route","timestamp":"2025-07-02T09:32:21.908Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/correlation/event-to-debris, POST} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchDebrisController {/debris}:","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/search, POST} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/names, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/object-classes, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/missions, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/countries, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/debris/debug-query-dto, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchSatelliteController {/satellite}:","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/search, POST} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/names, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/statuses, GET} route","timestamp":"2025-07-02T09:32:21.909Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/satellite/test-direct-query, POST} route","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchFreqController {/freq}:","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/freq/search, POST} route","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchOrbitController {/orbit}:","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/search, POST} route","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle, POST} route","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/bulk-tle/all, POST} route","timestamp":"2025-07-02T09:32:21.910Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, GET} route","timestamp":"2025-07-02T09:32:21.911Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/orbit/tle-config, PUT} route","timestamp":"2025-07-02T09:32:21.911Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLoopholeController {/loophole}:","timestamp":"2025-07-02T09:32:21.911Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/loophole/search, POST} route","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchNewsController {/api/es/news}:","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/indices, GET} route","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translation-status, GET} route","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/translate, POST} route","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/api-stats, GET} route","timestamp":"2025-07-02T09:32:21.912Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failure-stats, GET} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/reset-failure-stats, POST} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/failed-translations, GET} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/retry-failed, POST} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/extract-themes, POST} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/list, POST} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/news/hot-themes, GET} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchLaunchController {/api/es/launch}:","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/search, POST} route","timestamp":"2025-07-02T09:32:21.913Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/rocket-names, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/site-names, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/providers, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/cospar, POST} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-names, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/all-launch-sites, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/wiki-site-info, GET} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/launch/service-providers/search, POST} route","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RoutesResolver","level":"info","message":"ElasticsearchRocketController {/api/es/rocket}:","timestamp":"2025-07-02T09:32:21.914Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/es/rocket/search, POST} route","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RoutesResolver","level":"info","message":"OrbitCalculatorController {/api/orbit-calculator}:","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/orbit-calculator/calculate-positions, POST} route","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RoutesResolver","level":"info","message":"PassAnalysisController {/api/pass-analysis}:","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/pass-analysis/pass-analysis, POST} route","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RoutesResolver","level":"info","message":"ConjunctionAnalysisController {/api/conjunction-analysis}:","timestamp":"2025-07-02T09:32:21.915Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/conjunction-analysis/conjunction-analysis, POST} route","timestamp":"2025-07-02T09:32:21.916Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteController {/local/satellite}:","timestamp":"2025-07-02T09:32:21.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/search, POST} route","timestamp":"2025-07-02T09:32:21.916Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/names, GET} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/statuses, GET} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync, POST} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/sync-all, POST} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate, POST} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/clear, POST} route","timestamp":"2025-07-02T09:32:21.917Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-classes-local, GET} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/orbit-types-local, GET} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/:id, GET} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite, GET} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-aggregation, POST} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/test-custom-aggregation, POST} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/aggregate-all, POST} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/incremental-aggregate, POST} route","timestamp":"2025-07-02T09:32:21.918Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-orbit-info, POST} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/satellite/update-users, POST} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RoutesResolver","level":"info","message":"ConstellationController {/local/constellation}:","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/update, POST} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/local/constellation/with-tle, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RoutesResolver","level":"info","message":"AggregationTaskController {/tasks/aggregation}:","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/latest, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/running, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id, GET} route","timestamp":"2025-07-02T09:32:21.919Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation, POST} route","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/start, POST} route","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/update-progress, POST} route","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/complete, POST} route","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tasks/aggregation/:id/fail, POST} route","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RoutesResolver","level":"info","message":"DatabaseController {/api/v1/database}:","timestamp":"2025-07-02T09:32:21.920Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/query, POST} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/filter-satellites, POST} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-users, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/satellite-purposes, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-contractors, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-sites, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/database/launch-vehicles, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteTilesController {/tiles}:","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/generate, POST} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/status, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/satellites, GET} route","timestamp":"2025-07-02T09:32:21.921Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/tiles/metadata, GET} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RoutesResolver","level":"info","message":"NewsSchedulerController {/api/news-scheduler}:","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/status, GET} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/trigger, POST} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/config, POST} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/stop, POST} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/news-scheduler/cron-expressions, GET} route","timestamp":"2025-07-02T09:32:21.922Z"}
{"context":"RoutesResolver","level":"info","message":"SatelliteSchedulerController {/api/satellite-scheduler}:","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/status, GET} route","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/trigger, POST} route","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/config, POST} route","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/stop, POST} route","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/satellite-scheduler/cron-expressions, GET} route","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RoutesResolver","level":"info","message":"LLMConfigController {/llm-config}:","timestamp":"2025-07-02T09:32:21.923Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, GET} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/:configType, GET} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config, PUT} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/test, POST} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/reset, POST} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats/summary, GET} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/stats, DELETE} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/llm-config/export/all, GET} route","timestamp":"2025-07-02T09:32:21.924Z"}
{"context":"DatabaseService","level":"info","message":"正在检查并启用pg_trgm扩展...","timestamp":"2025-07-02T09:32:21.930Z"}
{"context":"DatabaseService","level":"info","message":"pg_trgm扩展已启用","timestamp":"2025-07-02T09:32:21.932Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-07-02T09:32:21.943Z"}
