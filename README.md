# 太空大数据平台后端服务## 项目概述

本项目是太空大数据平台的后端服务，提供航空航天相关的数据管理、翻译服务、轨道计算等功能。

## 主要功能

### 1. 新闻数据管理
- **自动翻译服务**：支持新闻内容的中英文翻译，支持批量翻译和特定文档ID翻译
- **主题词提取**：自动从新闻内容中提取关键主题词
- **内容爬取**：从外部URL获取新闻全文内容
- **数据聚合**：支持批量处理和定时任务
- **精准翻译**：新增documentId参数，支持针对特定文档进行重新翻译

### 2. 卫星轨道数据
- **TLE轨道数据**：提供双行根数轨道数据查询
- **轨道计算**：实时计算卫星位置和轨道信息
- **合取分析**：计算卫星间的接近事件
- **过境分析**：计算卫星对地面站点的可见性

### 3. 空间碎片信息
- **碎片数据库**：管理空间碎片的轨道信息
- **碎片事件**：记录和分析碎片产生事件
- **风险评估**：碎片碰撞风险分析

### 4. 漏洞信息管理
- **CVE数据**：航空航天相关的安全漏洞信息
- **风险评级**：漏洞严重性评估和分类

## 技术架构

- **框架**：NestJS + TypeScript
- **数据库**：PostgreSQL + Elasticsearch
- **缓存**：Redis
- **API文档**：Swagger/OpenAPI
- **认证**：JWT + 角色权限控制

## 最近更新记录

### 2025-01-29 翻译功能重大修复

**问题描述**：
1. **强制重新翻译不生效**：设置`forceRetranslate: true`后仍使用缓存结果
2. **翻译内容截断**：长文本翻译后`content_cn`字段比原文`content`字段短很多

**修复内容**：

#### 1. forceRetranslate功能修复
- **翻译服务层修复**：
  - 为`translateText`方法添加`forceRetranslate`参数
  - 为`translateAndExtractInOneCall`方法添加`forceRetranslate`参数
  - 在强制重新翻译模式下绕过所有缓存机制

- **新闻服务层修复**：
  - 正确传递`forceRetranslate`参数到翻译服务
  - 包括组合翻译、分别翻译、重试翻译等所有场景

#### 2. 翻译内容截断问题修复
- **长文本分段处理优化**：
  - 分段翻译失败时保留原文而不是错误标记
  - 避免用`[翻译失败...]`等标记替换原始内容
  - 改进错误处理策略，最大化保留内容完整性

- **5层智能分段策略保持**：
  1. 按段落分割（双换行符）
  2. 按句子分割（句号、问号、感叹号）
  3. 按单行分割（单换行符）
  4. 按词汇边界分割（空格）
  5. 强制分割（兜底策略）

#### 3. 64K上下文优化保持
- **模型配置优化**：
  - qwen-turbo: maxTextLength 25000, maxTokens 7000
  - qwen-max: maxTextLength 45000, maxTokens 30000
  - 严格遵循各模型的token限制避免API错误

**测试验证**：
- 创建专门的诊断脚本 `test-translation-diagnosis.js`
- 测试强制重新翻译功能
- 检查翻译内容质量和长度比例
- 验证长文本翻译效果

**使用示例**：
```bash
# 强制重新翻译示例
curl -X 'POST' \
  'http://localhost:3001/api/es/news/translate' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "batchSize": 30,
    "maxDocs": 50,
    "forceRetranslate": true,
    "autoExtractThemes": true
  }'

# 针对特定文档ID翻译示例
curl -X 'POST' \
  'http://localhost:3001/api/es/news/translate' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "documentId": "bbfa7e8c6c4ff26acf0e27f7fff9ba97",
    "specificIndexes": ["news_space"],
    "forceRetranslate": true,
    "autoExtractThemes": true,
    "llmMode": "high_quality"
  }'

# 运行诊断测试
node test-translation-diagnosis.js
```

**预期效果**：
- `forceRetranslate: true`时确实重新调用大模型API
- 长文本翻译后内容不再截断，保持合理的长度比例
- 翻译失败的段落保留原文而不是错误标记

## API 文档

服务启动后访问：`http://localhost:3001/api-docs`

## 环境配置

### 环境变量
```bash
# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=spacedata
DATABASE_USER=postgres
DATABASE_PASSWORD=your_password

# Elasticsearch配置
ELASTICSEARCH_HOST=localhost:9200

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 大模型配置
QWEN_API_KEY=your_qwen_api_key
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1
TRANSLATION_MODE=default
```

### 快速启动
```bash
# 安装依赖
npm install

# 启动开发环境
npm run start:dev

# 构建生产版本
npm run build
npm run start:prod
```

## 开发指南

### 代码规范
- 使用TypeScript严格模式
- 遵循NestJS最佳实践
- 所有API需要Swagger文档
- 完善的错误处理和日志记录

### 测试
```bash
# 运行单元测试
npm run test

# 运行测试覆盖率
npm run test:cov

# 运行翻译功能诊断
node test-translation-diagnosis.js
```

### 数据库迁移
```bash
# 生成新的迁移文件
npm run migration:generate -- --name YourMigrationName

# 运行迁移
npm run migration:run

# 回滚迁移
npm run migration:revert
```

## 部署说明

详见 `DEPLOYMENT.md` 文件。

## 许可证

本项目为内部使用项目。

---

**维护团队**：空天未来研发团队
**最后更新**：2025-01-29