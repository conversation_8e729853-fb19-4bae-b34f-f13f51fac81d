/**
 * 大模型配置文件
 * 用于管理所有大模型相关的配置项
 * 
 * 优化说明：基于64K上下文长度能力，大幅提升处理限制
 * - 64K tokens ≈ 约50,000-60,000字符
 * - 考虑输入+输出占用，合理分配上下文空间
 */

/**
 * 支持的大模型提供商
 */
export enum LLMProvider {
  QWEN = 'qwen',
  OPENAI = 'openai',
  CLAUDE = 'claude',
  GLM = 'glm'
}

/**
 * 大模型配置接口
 */
export interface LLMConfig {
  provider: LLMProvider;
  model: string;
  apiKey: string;
  baseURL?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
  maxConcurrentRequests?: number;
}

/**
 * 翻译配置接口
 */
export interface TranslationConfig extends LLMConfig {
  systemPrompt: string;
  maxTextLength: number;
  batchSize?: number;
}

/**
 * 主题提取配置接口
 */
export interface ThemeExtractionConfig extends LLMConfig {
  systemPrompt: string;
  maxContentLength: number;
  maxThemes?: number;
}

/**
 * 默认的翻译配置
 * 根据模型实际限制优化配置
 */
export const DEFAULT_TRANSLATION_CONFIG: TranslationConfig = {
  provider: LLMProvider.QWEN,
  model: 'qwen-turbo', // 默认使用qwen-turbo模型
  apiKey: process.env.QWEN_API_KEY || 'sk-1b581fd6f319419d9d0e3f2cc855c10d',
  baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  maxTokens: 7000, // qwen-turbo限制8192，设置7000留余量
  temperature: 0.1,
  timeout: 60000, // 增加超时时间以应对长文本处理
  maxRetries: 2,
  retryDelay: 1000,
  maxConcurrentRequests: 3,
  systemPrompt: '你是一位专业翻译，特别是很擅长在航空航天领域的翻译，将以下内容准确翻译为中文，保留数字和专有名词格式。**重要：必须保留原文中的所有换行符（\\n）、段落格式和缩进空格，翻译后的中文内容应与原文具有完全相同的换行结构和缩进格式**',
  maxTextLength: 25000, // 调整到适合qwen-turbo处理的长度
  batchSize: 10
};

/**
 * 默认的主题提取配置
 * 根据模型限制优化配置
 */
export const DEFAULT_THEME_EXTRACTION_CONFIG: ThemeExtractionConfig = {
  provider: LLMProvider.QWEN,
  model: 'qwen-turbo', // 默认使用qwen-turbo模型
  apiKey: process.env.QWEN_API_KEY || 'sk-1b581fd6f319419d9d0e3f2cc855c10d',
  baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
  maxTokens: 2000, // qwen-turbo主题提取输出较短，设置合理值
  temperature: 0.2,
  timeout: 45000, // 增加超时时间
  maxRetries: 2,
  retryDelay: 1000,
  maxConcurrentRequests: 3,
  systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。',
  maxContentLength: 20000, // 调整到适合qwen-turbo处理的长度
  maxThemes: 5
};

/**
 * 可选的大模型配置（用于不同场景）
 */
export const ALTERNATIVE_CONFIGS = {
  // 超长文本处理配置（使用qwen-max，充分利用64K上下文）
  ULTRA_LONG_TRANSLATION: {
    ...DEFAULT_TRANSLATION_CONFIG,
    model: 'qwen-max-latest',
    temperature: 0.05,
    maxTokens: 30000, // qwen-max支持更大的token输出
    maxTextLength: 45000, // 接近64K上下文的极限利用
    maxConcurrentRequests: 1, // 减少并发以保证稳定性
    timeout: 120000, // 超长文本需要更长处理时间
    maxRetries: 3 // 增加重试次数
  } as TranslationConfig,

  // 高质量翻译配置（使用qwen-max）
  HIGH_QUALITY_TRANSLATION: {
    ...DEFAULT_TRANSLATION_CONFIG,
    model: 'qwen-max-latest',
    temperature: 0.05,
    maxTokens: 25000, // qwen-max支持更大的token输出
    maxTextLength: 40000, // 高质量模式支持更长的输入
    maxConcurrentRequests: 2, // 减少并发以提高稳定性
    timeout: 90000 // 增加超时时间
  } as TranslationConfig,

  // 快速翻译配置（使用qwen-turbo）
  FAST_TRANSLATION: {
    ...DEFAULT_TRANSLATION_CONFIG,
    model: 'qwen-turbo',
    temperature: 0.2,
    maxTokens: 6000, // qwen-turbo限制8192，设置6000为快速模式
    maxTextLength: 20000, // 快速模式适中设置
    maxConcurrentRequests: 5,
    timeout: 45000
  } as TranslationConfig,

  // 超长主题提取配置（使用qwen-max）
  ULTRA_LONG_THEME_EXTRACTION: {
    ...DEFAULT_THEME_EXTRACTION_CONFIG,
    model: 'qwen-max-latest',
    temperature: 0.1,
    maxTokens: 6000, // qwen-max支持更大输出
    maxContentLength: 40000, // 支持更长内容的主题提取
    maxConcurrentRequests: 2,
    timeout: 90000,
    systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。'
  } as ThemeExtractionConfig,

  // 高质量主题提取配置（使用qwen-max）
  HIGH_QUALITY_THEME_EXTRACTION: {
    ...DEFAULT_THEME_EXTRACTION_CONFIG,
    model: 'qwen-max-latest',
    temperature: 0.1,
    maxTokens: 5000, // qwen-max支持更大输出
    maxContentLength: 35000,
    maxConcurrentRequests: 2,
    timeout: 75000,
    systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。'
  } as ThemeExtractionConfig,

  // 快速主题提取配置（使用qwen-turbo）
  FAST_THEME_EXTRACTION: {
    ...DEFAULT_THEME_EXTRACTION_CONFIG,
    model: 'qwen-turbo',
    temperature: 0.3,
    maxTokens: 1500, // qwen-turbo快速模式，设置较小值
    maxContentLength: 15000, // 快速模式处理较短文本
    maxConcurrentRequests: 5,
    timeout: 30000,
    systemPrompt: '你是一位专业的文本分析专家，擅长提取文章的主题词和识别内容类型。请从以下文本中（可能是中文或英文）：1）提取不超过5个最重要的主题词，这些词应该能够概括文章的核心内容，主题词必须用中文表示；2）识别文章类型，请严格按照以下标准判断：科普类型-文章主要目的是教育和知识普及，重点解释科学原理、技术概念、工作机制等，具有明显的教学性质；军事类型-文章涉及军事装备、军事行动、国防安全、武器系统等；新闻事实-文章主要报道具体事件、发布消息、描述事实，即使涉及技术话题也主要是事实报道而非教学解释。判断原则：只有当文章明确以教育为目的、主要在解释概念和原理时才标记为科普；单纯的技术新闻报道不应标记为科普。如果判断为科普类型，请在主题词中添加"科普"；如果判断为军事类型，请在主题词中添加"军事"；如果都不是，则不添加类型标签。只返回最终的中文主题词，用逗号分隔，不要有其他内容。'
  } as ThemeExtractionConfig
};

/**
 * 获取翻译配置
 * @param mode 模式：'default' | 'high_quality' | 'fast' | 'ultra_long'
 * @returns 翻译配置
 */
export function getTranslationConfig(mode: 'default' | 'high_quality' | 'fast' | 'ultra_long' = 'default'): TranslationConfig {
  switch (mode) {
    case 'ultra_long':
      return ALTERNATIVE_CONFIGS.ULTRA_LONG_TRANSLATION;
    case 'high_quality':
      return ALTERNATIVE_CONFIGS.HIGH_QUALITY_TRANSLATION;
    case 'fast':
      return ALTERNATIVE_CONFIGS.FAST_TRANSLATION;
    default:
      return DEFAULT_TRANSLATION_CONFIG;
  }
}

/**
 * 获取主题提取配置
 * @param mode 模式：'default' | 'high_quality' | 'fast' | 'ultra_long'
 * @returns 主题提取配置
 */
export function getThemeExtractionConfig(mode: 'default' | 'high_quality' | 'fast' | 'ultra_long' = 'default'): ThemeExtractionConfig {
  switch (mode) {
    case 'ultra_long':
      return ALTERNATIVE_CONFIGS.ULTRA_LONG_THEME_EXTRACTION;
    case 'high_quality':
      return ALTERNATIVE_CONFIGS.HIGH_QUALITY_THEME_EXTRACTION;
    case 'fast':
      return ALTERNATIVE_CONFIGS.FAST_THEME_EXTRACTION;
    default:
      return DEFAULT_THEME_EXTRACTION_CONFIG;
  }
}

/**
 * 自定义配置合并函数
 * @param baseConfig 基础配置
 * @param customConfig 自定义配置
 * @returns 合并后的配置
 */
export function mergeConfig<T extends LLMConfig>(baseConfig: T, customConfig: Partial<T>): T {
  return {
    ...baseConfig,
    ...customConfig
  };
}

/**
 * 验证大模型配置
 * @param config 配置对象
 * @throws 如果配置无效则抛出错误
 */
export function validateLLMConfig(config: LLMConfig): void {
  if (!config.apiKey) {
    throw new Error('API密钥不能为空');
  }
  
  if (!config.model) {
    throw new Error('模型名称不能为空');
  }
  
  if (config.maxRetries !== undefined && config.maxRetries < 0) {
    throw new Error('最大重试次数不能小于0');
  }
  
  if (config.timeout !== undefined && config.timeout <= 0) {
    throw new Error('超时时间必须大于0');
  }
  
  if (config.maxConcurrentRequests !== undefined && config.maxConcurrentRequests <= 0) {
    throw new Error('最大并发请求数必须大于0');
  }
} 